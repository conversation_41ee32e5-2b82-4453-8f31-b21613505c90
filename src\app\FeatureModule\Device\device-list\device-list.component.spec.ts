import { CommonModule } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { Subject } from 'rxjs';
import { ITEMS_PER_PAGE } from '../../../app.constants';
import { ConfirmDialogService } from '../../../confirmationdialog/confirmation.service';
import { NoDataMessageComponent } from '../../NoData/no-data-message/no-data-message.component';
import { ListingPageReloadSubjectParameter } from '../../../model/common/listingPageReloadSubjectParameter.model';
import { Pageable } from '../../../model/common/pageable.model';
import { Sort } from '../../../model/common/sort.model';
import { IDevice } from '../../../model/device.model';
import { DeviceSearchRequest } from '../../../model/device/deviceSearchRequest.model';
import { DeviceListResponse } from '../../../model/deviceListResponst.model';
import { BasicSalesOrderDetailResponse } from '../../../model/SalesOrder/BasicSalesOrderDetailResponse.model';
import { AuthJwtService } from '../../../shared/auth-jwt.service';
import { DeviceService } from '../../../shared/device.service';
import { ProductStatusEnum } from '../../../shared/enum/Common/ProductStatus.enum';
import { deviceTypesEnum } from '../../../shared/enum/deviceTypesEnum.enum';
import { collapseFilterTextEnum } from '../../../shared/enum/collapseFilterButtonText.enum';

import { ImportCsvFileApiService } from '../../../shared/importFileService/import-csv-file-api.service';
import { CustomerAssociationService } from '../../../shared/modalservice/customer-association.service';
import { PermissionService } from '../../../shared/permission.service';
import { EnumMappingDisplayNamePipe } from '../../../shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { DeviceTypeNamePipe } from '../../../shared/pipes/device-type-name.pipe';
import { PrintListPipe } from '../../../shared/pipes/printList.pipe';
import { HidePermissionNamePipe } from '../../../shared/pipes/Role/hidePermissionName.pipe';
import { CountryCacheService } from '../../../shared/Service/CacheService/countrycache.service';
import { DeviceOperationService } from '../DeviceService/Device-Operation/device-operation.service';
import { RoleApiCallService } from '../../../shared/Service/RoleService/role-api-call.service';
import { SalesOrderApiCallService } from '../../../shared/Service/SalesOrderService/sales-order-api-call.service';
import { SSOLoginService } from '../../../shared/Service/SSO/ssologin.service';
import { CommonOperationsService } from '../../../shared/util/common-operations.service';
import { CommonsService } from '../../../shared/util/commons.service';
import { KeyValueMappingServiceService } from '../../../shared/util/key-value-mapping-service.service';
import { commonsProviders } from '../../../Tesing-Helper/test-utils';
import { DeviceFilterComponent } from '../device-filter/device-filter.component';
import { DeviceFilterAction } from '../../../model/device/DeviceFilterAction.model';
import { DeviceComponent } from './device-list.component';


describe('DeviceComponent', () => {
  let component: DeviceComponent;
  let fixture: ComponentFixture<DeviceComponent>;
  let localStorageServiceMock: jasmine.SpyObj<LocalStorageService>;
  let authServiceSpy: jasmine.SpyObj<AuthJwtService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let deviceServiceMock: jasmine.SpyObj<DeviceService>;
  let countryCacheServiceSpy: jasmine.SpyObj<CountryCacheService>;
  let importCsvfileApiServiceSpy: jasmine.SpyObj<ImportCsvFileApiService>;
  let salesOrderApiCallServiceSpy: jasmine.SpyObj<SalesOrderApiCallService>;
  let deviceOperationServiceSpy: jasmine.SpyObj<DeviceOperationService>;
  let customerAssociationServiceSpy: jasmine.SpyObj<CustomerAssociationService>;

  let deviceList: Array<IDevice> = [
    new IDevice(
      63,
      "maitri-test-function-audit1",
      "1.0.0.0",
      ProductStatusEnum.DISABLED,
      null,
      true,
      deviceTypesEnum.DEMO_DEVICE,
      true,
      null,
      "20250123",
      "mohit1",
      "Japan"
    ),
    new IDevice(
      64,
      "maitri-test-function-audit2",
      "1.0.0.0",
      ProductStatusEnum.DISABLED,
      null,
      true,
      deviceTypesEnum.DEMO_DEVICE,
      false,
      null,
      "20250123",
      "mohit1",
      "Japan"
    ),
    new IDevice(
      65,
      "maitri-test-function-audit3",
      "1.0.0.0",
      ProductStatusEnum.DISABLED,
      null,
      true,
      deviceTypesEnum.TEST_DEVICE,
      true,
      null,
      "20250123",
      "mohit1",
      "Japan"
    )
  ];

  const sort = new Sort(true, false, false);
  const pageable = new Pageable(sort, 0, 10, 0, true, false);
  let deviceListResponse: DeviceListResponse = new DeviceListResponse(pageable, 3, false, 3, 10, true, sort, 10, 0, false, deviceList);

  // Mock BasicSalesOrderDetailResponse
  const mockBasicSalesOrderDetailResponse: BasicSalesOrderDetailResponse = new BasicSalesOrderDetailResponse(
    'SO123',
    'Test Customer',
    '<EMAIL>',
    1,
    'PO123',
    false,
    false,
    'Standard'
  );



  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info', 'clear']);
    localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getDevicePermission', 'getProbPermission', 'getJobPermission', 'getSoftwearBuildPermission', 'getDeviceLogPermission', 'getUserPermission', 'getVideoPermission', 'getRolePermission', 'getKitManagementPermission', 'getSalesOrderPermission', 'getCountryPermission', 'getAuditPermission', 'getProbeConfigGroupPermission']);
    deviceServiceMock = jasmine.createSpyObj('DeviceService', ['getDeviceList', 'getpackageVersion', 'editEnableDisableForDevice', 'updateDeviceState', 'updateDeviceTypeToTest', 'updateDeviceTypeToClient', 'updateDeviceTypeToDemo', 'rmaProductStatusForDevice', 'disableProductStatusForDevice', 'generateCSVFileForDevice', 'downloadCSVFileForDevice', 'getDeviceDetail', 'getReleaseVersionDetail', 'associationDeviceWithSalesOrder', 'assignSelectedReleaseVersion']);
    authServiceSpy = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    countryCacheServiceSpy = jasmine.createSpyObj('CountryCacheServiceSpy', ['getCountryListFromCache']);
    importCsvfileApiServiceSpy = jasmine.createSpyObj('ImportCsvfileApiService', ['downloadCsvTemplate', 'importFileForUpdateTemplateData']);
    salesOrderApiCallServiceSpy = jasmine.createSpyObj('SalesOrderApiCallService', ['getSalesOrderNumberList', 'getBasicSalesOrderDetails']);
    deviceOperationServiceSpy = jasmine.createSpyObj('DeviceOperationService', [
      'getDeviceListFilterRequestParameterSubject',
      'getDeviceListRefreshSubject',
      'callRefreshPageSubject',
      'loadDeviceList',
      'updateCacheInBackground',
      'updateSalesOrderCacheOnly',
      'validateDeviceSelection',
      'validateUserCountryAccess',
      'convertDevicesToTest',
      'convertDevicesToClient',
      'convertDevicesToDemo',
      'lockUnlockDevices',
      'enableDisableDevices',
      'associateDevicesWithSalesOrder',
      'disableProductStatusForDevices',
      'rmaProductStatusForDevices',
      'exportDeviceCSV',
      'clearAllFiltersAndRefresh',
      'handleDeviceOperation'
    ]);

    customerAssociationServiceSpy = jasmine.createSpyObj('CustomerAssociationService', ['openCustomerAssociationPopup']);

    // Setup DeviceOperationService subjects
    const deviceFilterSubject = new Subject<DeviceFilterAction>();
    const deviceRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();
    deviceOperationServiceSpy.getDeviceListFilterRequestParameterSubject.and.returnValue(deviceFilterSubject);
    deviceOperationServiceSpy.getDeviceListRefreshSubject.and.returnValue(deviceRefreshSubject);

    // Mock loadDeviceList to return the device list response
    deviceOperationServiceSpy.loadDeviceList.and.returnValue(Promise.resolve({
      success: true,
      devices: deviceListResponse.content,
      totalDeviceDisplay: deviceListResponse.numberOfElements,
      totalDevice: deviceListResponse.totalElements,
      localDeviceList: deviceListResponse.content,
      totalItems: deviceListResponse.totalElements
    }));

    await TestBed.configureTestingModule({
      declarations: [
        DeviceComponent,
        NoDataMessageComponent,
        DeviceTypeNamePipe,
        EnumMappingDisplayNamePipe,
        DeviceFilterComponent
      ],
      imports: [CommonModule, ReactiveFormsModule, FormsModule, NgbPaginationModule, NgMultiSelectDropDownModule.forRoot()
      ],
      providers: [
        ConfirmDialogService,
        CommonsService,
        EnumMappingDisplayNamePipe,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: AuthJwtService, useValue: authServiceSpy },
        { provide: PermissionService, useValue: permissionServiceSpy },
        { provide: DeviceService, useValue: deviceServiceMock },
        { provide: CountryCacheService, useValue: countryCacheServiceSpy },
        { provide: ImportCsvFileApiService, useValue: importCsvfileApiServiceSpy },
        { provide: SalesOrderApiCallService, useValue: salesOrderApiCallServiceSpy },
        { provide: DeviceOperationService, useValue: deviceOperationServiceSpy },
        { provide: CustomerAssociationService, useValue: customerAssociationServiceSpy },
        SessionStorageService,
        SSOLoginService,
        RoleApiCallService,
        CommonOperationsService,
        HidePermissionNamePipe,
        PrintListPipe,
        KeyValueMappingServiceService,
        commonsProviders(toastrServiceMock)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DeviceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  // Test cases start here
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should initialize component when user is not authenticated', () => {
      authServiceSpy.isAuthenticate.and.returnValue(false);
      component.ngOnInit();
      expect(authServiceSpy.loginNavigate).toHaveBeenCalled();
    });

    it('should initialize component when user is authenticated', () => {
      authServiceSpy.isAuthenticate.and.returnValue(true);
      permissionServiceSpy.getDevicePermission.and.returnValue(true);
      spyOn(component, 'getDeviceData');
      component.ngOnInit();
      expect(component.getDeviceData).toHaveBeenCalled();
    });
  });

  describe('loadAll', () => {
    it('should load devices successfully', async () => {
      const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
      component.page = 1;
      component.itemsPerPage = 10;
      await component.loadAll(mockSearchRequest);
      expect(component.devices).toEqual(deviceListResponse.content);
      expect(component.loading).toBe(false);
    });
  });

  describe('loadPage', () => {
    it('should load new page when page is different', () => {
      component.previousPage = 1;
      spyOn(component, 'clearDeviceIdCheckBox');
      spyOn(component, 'filterPageSubjectCallForReloadPage');
      component.loadPage(2);
      expect(component.previousPage).toBe(2);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });
  });

  describe('changeDataSize', () => {
    it('should change data size and reload page', () => {
      const mockEvent = { target: { value: '25' } };
      spyOn(component, 'filterPageSubjectCallForReloadPage');
      component.changeDataSize(mockEvent);
      expect(component.itemsPerPage).toBe('25');
      expect(component.loading).toBe(true);
    });
  });

  describe('changeDeviceOperation', () => {
    it('should call device operation service', () => {
      const mockEvent = { target: { value: 'TEST_OPERATION' } };
      component.changeDeviceOperation(mockEvent);
      expect(deviceOperationServiceSpy.handleDeviceOperation).toHaveBeenCalledWith('TEST_OPERATION', component, 'deviceOperation');
    });
  });

  describe('onChangeDevice', () => {
    it('should add device to selection when checked', () => {
      const mockDevice = deviceList[0];
      const mockEvent = { target: { checked: true } };
      component.deviceIdList = [];
      component.selectedDeviceList = [];
      spyOn(component, 'defaultSelectAll');
      component.onChangeDevice(mockDevice, mockEvent);
      expect(component.deviceIdList).toContain(mockDevice.id);
      expect(component.defaultSelectAll).toHaveBeenCalled();
    });

    it('should remove device from selection when unchecked', () => {
      const mockDevice = deviceList[0];
      component.deviceIdList = [mockDevice.id];
      component.selectedDeviceList = [mockDevice];
      const mockEvent = { target: { checked: false } };
      spyOn(component, 'defaultSelectAll');
      component.onChangeDevice(mockDevice, mockEvent);
      expect(component.deviceIdList).not.toContain(mockDevice.id);
      expect(component.defaultSelectAll).toHaveBeenCalled();
    });
  });

  describe('convertDataToClient', () => {
    it('should convert devices to client', async () => {
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.convertDevicesToClient.and.returnValue(Promise.resolve(true));
      spyOn(component, 'clearDeviceIdCheckBox');
      await component.convertDataToClient();
      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });
  });

  describe('convertDataToDemo', () => {
    it('should convert devices to demo', async () => {
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.convertDevicesToDemo.and.returnValue(Promise.resolve(true));
      spyOn(component, 'clearDeviceIdCheckBox');
      await component.convertDataToDemo();
      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });
  });

  describe('convertDataToTest', () => {
    it('should convert devices to test', async () => {
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.convertDevicesToTest.and.returnValue(Promise.resolve(true));
      spyOn(component, 'clearDeviceIdCheckBox');
      await component.convertDataToTest();
      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });
  });

  describe('validateProductStatusForDisableAction', () => {
    it('should disable devices when validation passes and user confirms', async () => {
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);
      deviceOperationServiceSpy.disableProductStatusForDevices.and.returnValue(Promise.resolve(true));
      spyOn(component, 'clearDeviceIdCheckBox');
      const mockConfirmDialogService = TestBed.inject(ConfirmDialogService);
      spyOn(mockConfirmDialogService, 'getBasicModelConfigForDisableAction').and.returnValue({
        title: 'Confirm', message: 'Are you sure?', btnOkText: 'Yes', btnCancelText: 'No'
      });
      spyOn(mockConfirmDialogService, 'confirm').and.returnValue(Promise.resolve(true));
      await component.validateProductStatusForDisableAction();
      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });
  });

  describe('validateProductStatusForRMAAction', () => {
    it('should mark devices as RMA when validation passes and user confirms', async () => {
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);
      deviceOperationServiceSpy.rmaProductStatusForDevices.and.returnValue(Promise.resolve(true));
      spyOn(component, 'clearDeviceIdCheckBox');
      const mockConfirmDialogService = TestBed.inject(ConfirmDialogService);
      spyOn(mockConfirmDialogService, 'getBasicModelConfigForRMAAction').and.returnValue({
        title: 'Confirm RMA', message: 'Are you sure?', btnOkText: 'Yes', btnCancelText: 'No'
      });
      spyOn(mockConfirmDialogService, 'confirm').and.returnValue(Promise.resolve(true));
      await component.validateProductStatusForRMAAction();
      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });
  });

  describe('exportCSV', () => {
    it('should export CSV successfully', async () => {
      component.deviceIdList = [1, 2];
      deviceOperationServiceSpy.exportDeviceCSV.and.returnValue(Promise.resolve());
      spyOn(component, 'clearDeviceIdCheckBox');
      await component.exportCSV();
      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });
  });

  describe('clearDeviceIdCheckBox', () => {
    it('should clear device selection and uncheck all checkboxes', () => {
      component.deviceIdList = [1, 2, 3];
      component.selectedDeviceList = deviceList;
      const mockCheckboxes = [{ checked: true }, { checked: true }];
      const mockSelectAllCheckbox = { checked: true };
      spyOn(document, 'getElementsByName').and.returnValue(mockCheckboxes as any);
      spyOn(document, 'getElementById').and.returnValue(mockSelectAllCheckbox as any);
      component.clearDeviceIdCheckBox();
      expect(component.deviceIdList).toEqual([]);
      expect(component.selectedDeviceList).toEqual([]);
    });
  });

  describe('defaultSelectAll', () => {
    it('should check select all checkbox when all devices are selected', () => {
      component.localDeviceList = deviceList;
      component.deviceIdList = deviceList.map(d => d.id);
      const mockSelectAllCheckbox = { checked: false };
      spyOn(document, 'getElementById').and.returnValue(mockSelectAllCheckbox as any);
      component.defaultSelectAll();
      expect(mockSelectAllCheckbox.checked).toBe(true);
    });

    it('should handle case when select all checkbox does not exist', () => {
      component.localDeviceList = deviceList;
      component.deviceIdList = deviceList.map(d => d.id);
      spyOn(document, 'getElementById').and.returnValue(null);
      expect(() => component.defaultSelectAll()).not.toThrow();
    });
  });

  describe('selectAllDevice', () => {
    it('should select all devices when checkbox is checked', () => {
      component.localDeviceList = deviceList;
      component.deviceIdList = [];
      component.selectedDeviceList = [];
      const mockEvent = { target: { checked: true } };
      const mockCheckboxes = [{ checked: false }, { checked: false }];
      spyOn(document, 'getElementsByName').and.returnValue(mockCheckboxes as any);
      component.selectAllDevice(mockEvent);
      expect(component.deviceIdList).toEqual(deviceList.map(d => d.id));
      expect(component.selectedDeviceList).toEqual(deviceList);
    });

    it('should deselect all devices when checkbox is unchecked', () => {
      component.localDeviceList = deviceList;
      component.deviceIdList = deviceList.map(d => d.id);
      component.selectedDeviceList = [...deviceList];
      const mockEvent = { target: { checked: false } };
      const mockCheckboxes = [{ checked: true }, { checked: true }];
      spyOn(document, 'getElementsByName').and.returnValue(mockCheckboxes as any);
      component.selectAllDevice(mockEvent);
      expect(component.deviceIdList).toEqual([]);
      expect(component.selectedDeviceList).toEqual([]);
    });
  });

  describe('defaultSelectDevice', () => {
    it('should return true when device is selected', () => {
      component.deviceIdList = [63, 64];
      const result = component.defaultSelectDevice(63);
      expect(result).toBe(true);
    });

    it('should return false when device is not selected', () => {
      component.deviceIdList = [63, 64];
      const result = component.defaultSelectDevice(65);
      expect(result).toBe(false);
    });
  });

  describe('filterPageSubjectCallForReloadPage', () => {
    it('should call device operation service with correct parameters', () => {
      const clearFilter = true;
      const clearSort = false;
      component.deviceSearchRequestBody = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
      component.filterPageSubjectCallForReloadPage(clearFilter, clearSort);
      expect(deviceOperationServiceSpy.callRefreshPageSubject).toHaveBeenCalled();
    });
  });

  describe('clickOnRefreshButton', () => {
    it('should update filter cache and call refresh filter', () => {
      spyOn(component, 'updateFilterCacheInBackground');
      spyOn(component, 'refreshFilter');
      component.clickOnRefreshButton();
      expect(component.updateFilterCacheInBackground).toHaveBeenCalled();
      expect(component.refreshFilter).toHaveBeenCalled();
    });
  });

  describe('associationDeviceWithSalesOrder', () => {
    it('should call device operation service when validation passes and user confirms', async () => {
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);
      deviceOperationServiceSpy.associateDevicesWithSalesOrder.and.returnValue(Promise.resolve(true));
      spyOn(component, 'clearDeviceIdCheckBox');
      customerAssociationServiceSpy.openCustomerAssociationPopup.and.returnValue(Promise.resolve({
        button: true, basicSalesOrderDetailResponse: mockBasicSalesOrderDetailResponse, isSalesOrderNewAdd: false
      }));
      await component.associationDeviceWithSalesOrder();
      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });

    it('should not call service when validation fails', async () => {
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(false);
      await component.associationDeviceWithSalesOrder();
      expect(deviceOperationServiceSpy.associateDevicesWithSalesOrder).not.toHaveBeenCalled();
    });
  });

  describe('refreshFilter', () => {
    it('should reset page and call filter page subject for reload', () => {
      component.page = 5;
      component.previousPage = 3;
      spyOn(component, 'filterPageSubjectCallForReloadPage');
      component.refreshFilter();
      expect(component.page).toBe(0);
      expect(component.previousPage).toBe(1);
    });
  });

  describe('updateFilterCacheInBackground', () => {
    it('should call device operation service to update cache', () => {
      component.updateFilterCacheInBackground();
      expect(deviceOperationServiceSpy.updateCacheInBackground).toHaveBeenCalled();
    });
  });

  describe('updateSalesOrderCacheOnly', () => {
    it('should call device operation service to update sales order cache only', () => {
      component.updateSalesOrderCacheOnly();
      expect(deviceOperationServiceSpy.updateSalesOrderCacheOnly).toHaveBeenCalled();
    });
  });

  describe('deviceDetailModel', () => {
    it('should emit device ID and show device detail event', () => {
      const deviceId = 123;
      spyOn(component.deviceId, 'emit');
      spyOn(component.showDeviceDetail, 'emit');
      component.deviceDetailModel(deviceId);
      expect(component.deviceId.emit).toHaveBeenCalledWith(deviceId);
      expect(component.showDeviceDetail.emit).toHaveBeenCalled();
    });
  });

  describe('toggleFilter', () => {
    it('should toggle filter visibility and update button text when filter is visible', () => {
      component.isFilterHidden = false;
      component.hideShowFilterButtonText = collapseFilterTextEnum.HIDE_FILTER;
      spyOn(component, 'updateIsFilterComponentInitWithApicall');
      spyOn(component, 'updateListPageRefreshForbackToDetailPage');
      spyOn(component, 'updateIsFilterHidden').and.callThrough();

      component.toggleFilter();

      expect(component.updateIsFilterComponentInitWithApicall).toHaveBeenCalledWith(false);
      expect(component.updateListPageRefreshForbackToDetailPage).toHaveBeenCalledWith(false);
      expect(component.updateIsFilterHidden).toHaveBeenCalledWith(true);
      expect(component.hideShowFilterButtonText).toBe(collapseFilterTextEnum.SHOW_FILTER);
    });

    it('should toggle filter visibility and update button text when filter is hidden', () => {
      component.isFilterHidden = true;
      component.hideShowFilterButtonText = collapseFilterTextEnum.SHOW_FILTER;
      spyOn(component, 'updateIsFilterComponentInitWithApicall');
      spyOn(component, 'updateListPageRefreshForbackToDetailPage');
      spyOn(component, 'updateIsFilterHidden').and.callThrough();

      component.toggleFilter();

      expect(component.updateIsFilterComponentInitWithApicall).toHaveBeenCalledWith(false);
      expect(component.updateListPageRefreshForbackToDetailPage).toHaveBeenCalledWith(false);
      expect(component.updateIsFilterHidden).toHaveBeenCalledWith(false);
      expect(component.hideShowFilterButtonText).toBe(collapseFilterTextEnum.HIDE_FILTER);
    });
  });

  describe('lockUnlock', () => {
    it('should lock devices', async () => {
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.lockUnlockDevices.and.returnValue(Promise.resolve(true));
      spyOn(component, 'clearDeviceIdCheckBox');
      await component.lockUnlock(true);
      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });
  });

  describe('enableDisableDevice', () => {
    it('should enable devices', async () => {
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.enableDisableDevices.and.returnValue(Promise.resolve(true));
      spyOn(component, 'clearDeviceIdCheckBox');
      await component.enableDisableDevice(true);
      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });

  });

  describe('ngOnDestroy', () => {
    it('should unsubscribe from subscriptions when they exist', () => {
      const mockSubscription1 = jasmine.createSpyObj('Subscription', ['unsubscribe']);
      const mockSubscription2 = jasmine.createSpyObj('Subscription', ['unsubscribe']);
      component.subscriptionForCommonloading = mockSubscription1;
      component.subscriptionForDeviceListFilterRequestParameter = mockSubscription2;
      component.ngOnDestroy();
      expect(mockSubscription1.unsubscribe).toHaveBeenCalled();
      expect(mockSubscription2.unsubscribe).toHaveBeenCalled();
    });

    it('should handle undefined subscriptions gracefully', () => {
      component.subscriptionForCommonloading = undefined;
      component.subscriptionForDeviceListFilterRequestParameter = undefined;
      expect(() => component.ngOnDestroy()).not.toThrow();
    });
  });

  describe('subjectInit', () => {
    it('should handle device filter action', () => {
      const mockDeviceFilterAction: DeviceFilterAction = {
        listingPageReloadSubjectParameter: { isReloadData: true, isDefaultPageNumber: true, isClearFilter: false, isMovePrevPage: false, isOtherAction: false },
        deviceSearchRequest: new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null)
      };
      spyOn(component, 'loadAll');
      (component as any).subjectInit();
      const deviceFilterSubject = deviceOperationServiceSpy.getDeviceListFilterRequestParameterSubject();
      deviceFilterSubject.next(mockDeviceFilterAction);
      expect(component.loadAll).toHaveBeenCalledWith(mockDeviceFilterAction.deviceSearchRequest);
    });
  });





  describe('getDeviceData', () => {
    it('should initialize device data with default values', () => {
      // Arrange
      component.page = 5;
      component.isFilterHidden = true;
      component.isFilterComponentInitWithApicall = false;
      component.listPageRefreshForbackToDetailPage = true;
      spyOn(component, 'clearDeviceIdCheckBox');
      spyOn(component, 'updateIsFilterHidden');
      spyOn(component, 'updateIsFilterComponentInitWithApicall');
      spyOn(component, 'updateListPageRefreshForbackToDetailPage');

      // Act
      component.getDeviceData();

      // Assert
      expect(component.page).toBe(0);
      expect(component.drpselectsize).toBe(ITEMS_PER_PAGE);
      expect(component.itemsPerPage).toBe(ITEMS_PER_PAGE);
      expect(component.updateIsFilterHidden).toHaveBeenCalledWith(false);
      expect(component.updateIsFilterComponentInitWithApicall).toHaveBeenCalledWith(true);
      expect(component.updateListPageRefreshForbackToDetailPage).toHaveBeenCalledWith(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });
  });



  describe('update methods', () => {
    it('should update and emit isFilterComponentInitWithApicall', () => {
      spyOn(component.isFilterComponentInitWithApicallChange, 'emit');
      component.updateIsFilterComponentInitWithApicall(true);
      expect(component.isFilterComponentInitWithApicall).toBe(true);
      expect(component.isFilterComponentInitWithApicallChange.emit).toHaveBeenCalledWith(true);
    });

    it('should update and emit listPageRefreshForbackToDetailPage', () => {
      spyOn(component.listPageRefreshForbackToDetailPageChange, 'emit');
      component.updateListPageRefreshForbackToDetailPage(true);
      expect(component.listPageRefreshForbackToDetailPage).toBe(true);
      expect(component.listPageRefreshForbackToDetailPageChange.emit).toHaveBeenCalledWith(true);
    });

    it('should update and emit deviceSearchRequestBody', () => {
      const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
      spyOn(component.deviceSearchRequestBodyChange, 'emit');
      component.updateDeviceSearchRequestBody(mockSearchRequest);
      expect(component.deviceSearchRequestBody).toBe(mockSearchRequest);
      expect(component.deviceSearchRequestBodyChange.emit).toHaveBeenCalledWith(mockSearchRequest);
    });

    it('should update and emit isFilterHidden', () => {
      spyOn(component.isFilterHiddenChange, 'emit');
      component.updateIsFilterHidden(true);
      expect(component.isFilterHidden).toBe(true);
      expect(component.isFilterHiddenChange.emit).toHaveBeenCalledWith(true);
    });
  });

  // ==================== ADDITIONAL COMPREHENSIVE TESTS ====================

  describe('loadAll - comprehensive scenarios', () => {
    it('should handle failed device list loading', async () => {
      const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
      deviceOperationServiceSpy.loadDeviceList.and.returnValue(Promise.resolve({
        success: false,
        devices: [],
        totalDeviceDisplay: 0,
        totalDevice: 0,
        localDeviceList: [],
        totalItems: 0
      }));

      component.page = 1;
      component.itemsPerPage = 10;

      await component.loadAll(mockSearchRequest);

      expect(component.devices).toEqual([]);
      expect(component.totalDeviceDisplay).toBe(0);
      expect(component.totalDevice).toBe(0);
      expect(component.localDeviceList).toEqual([]);
      expect(component.totalItems).toBe(0);
      expect(component.loading).toBe(false);
    });

    it('should emit deviceSearchRequestBodyChange when loading', async () => {
      const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
      spyOn(component.deviceSearchRequestBodyChange, 'emit');

      await component.loadAll(mockSearchRequest);

      expect(component.deviceSearchRequestBodyChange.emit).toHaveBeenCalledWith(mockSearchRequest);
    });
  });

  describe('loadPage - edge cases', () => {
    it('should not reload when page is the same as previous page', () => {
      component.previousPage = 2;
      spyOn(component, 'clearDeviceIdCheckBox');
      spyOn(component, 'filterPageSubjectCallForReloadPage');

      component.loadPage(2);

      expect(component.clearDeviceIdCheckBox).not.toHaveBeenCalled();
      expect(component.filterPageSubjectCallForReloadPage).not.toHaveBeenCalled();
    });
  });

  describe('changeDataSize - comprehensive', () => {
    it('should clear device selections when changing data size', () => {
      const mockEvent = { target: { value: '50' } };
      component.deviceIdList = [1, 2, 3];
      component.selectedDeviceList = deviceList;
      spyOn(component, 'filterPageSubjectCallForReloadPage');

      component.changeDataSize(mockEvent);

      expect(component.deviceIdList).toEqual([]);
      expect(component.selectedDeviceList).toEqual([]);
      expect(component.itemsPerPage).toBe('50');
      expect(component.loading).toBe(true);
      expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(true, false);
    });
  });

  describe('onChangeDevice - edge cases', () => {
    it('should handle device not found in selectedDeviceList when unchecking', () => {
      const mockDevice = deviceList[0];
      component.deviceIdList = [mockDevice.id];
      component.selectedDeviceList = []; // Empty list
      const mockEvent = { target: { checked: false } };
      spyOn(component, 'defaultSelectAll');

      component.onChangeDevice(mockDevice, mockEvent);

      expect(component.deviceIdList).not.toContain(mockDevice.id);
      expect(component.defaultSelectAll).toHaveBeenCalled();
    });
  });

  describe('selectAllDevice - comprehensive', () => {
    it('should handle devices already in selection when selecting all', () => {
      component.localDeviceList = deviceList;
      component.deviceIdList = [deviceList[0].id]; // One device already selected
      component.selectedDeviceList = [deviceList[0]];
      const mockEvent = { target: { checked: true } };
      const mockCheckboxes = [{ checked: false }, { checked: false }, { checked: false }];
      spyOn(document, 'getElementsByName').and.returnValue(mockCheckboxes as any);

      component.selectAllDevice(mockEvent);

      expect(component.deviceIdList).toEqual(deviceList.map(d => d.id));
      expect(component.selectedDeviceList).toEqual(deviceList);
    });

    it('should handle removing devices when deselecting all', () => {
      component.localDeviceList = deviceList;
      component.deviceIdList = deviceList.map(d => d.id);
      component.selectedDeviceList = [...deviceList];
      const mockEvent = { target: { checked: false } };
      const mockCheckboxes = [{ checked: true }, { checked: true }, { checked: true }];
      spyOn(document, 'getElementsByName').and.returnValue(mockCheckboxes as any);

      component.selectAllDevice(mockEvent);

      expect(component.deviceIdList).toEqual([]);
      expect(component.selectedDeviceList).toEqual([]);
      mockCheckboxes.forEach(checkbox => {
        expect(checkbox.checked).toBe(false);
      });
    });
  });

  describe('defaultSelectAll - edge cases', () => {
    it('should handle empty localDeviceList', () => {
      component.localDeviceList = [];
      component.deviceIdList = [];
      const mockSelectAllCheckbox = { checked: false };
      spyOn(document, 'getElementById').and.returnValue(mockSelectAllCheckbox as any);

      component.defaultSelectAll();

      expect(mockSelectAllCheckbox.checked).toBe(false);
    });

    it('should set checkbox to false when not all devices are selected', () => {
      component.localDeviceList = deviceList;
      component.deviceIdList = [deviceList[0].id]; // Only first device selected
      const mockSelectAllCheckbox = { checked: true };
      spyOn(document, 'getElementById').and.returnValue(mockSelectAllCheckbox as any);

      component.defaultSelectAll();

      expect(mockSelectAllCheckbox.checked).toBe(false);
    });
  });

  describe('clearDeviceIdCheckBox - comprehensive', () => {
    it('should handle case when selectDevice element does not exist', () => {
      component.deviceIdList = [1, 2, 3];
      component.selectedDeviceList = deviceList;
      const mockCheckboxes = [{ checked: true }, { checked: true }];
      spyOn(document, 'getElementsByName').and.returnValue(mockCheckboxes as any);
      spyOn(document, 'getElementById').and.returnValue(null);

      expect(() => component.clearDeviceIdCheckBox()).not.toThrow();
      expect(component.deviceIdList).toEqual([]);
      expect(component.selectedDeviceList).toEqual([]);
    });
  });

  describe('associationDeviceWithSalesOrder - comprehensive', () => {
    it('should handle user canceling the association dialog', async () => {
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);
      customerAssociationServiceSpy.openCustomerAssociationPopup.and.returnValue(Promise.resolve({
        button: false, basicSalesOrderDetailResponse: null, isSalesOrderNewAdd: false
      }));

      await component.associationDeviceWithSalesOrder();

      expect(deviceOperationServiceSpy.associateDevicesWithSalesOrder).not.toHaveBeenCalled();
    });

    it('should handle dialog dismissal (ESC key or click outside)', async () => {
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);
      customerAssociationServiceSpy.openCustomerAssociationPopup.and.returnValue(Promise.reject('Dialog dismissed'));

      await component.associationDeviceWithSalesOrder();

      expect(deviceOperationServiceSpy.associateDevicesWithSalesOrder).not.toHaveBeenCalled();
      expect(component.loading).toBe(false);
    });
  });

  describe('validateProductStatusForDisableAction - comprehensive', () => {
    it('should handle user canceling the disable confirmation', async () => {
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);
      const mockConfirmDialogService = TestBed.inject(ConfirmDialogService);
      spyOn(mockConfirmDialogService, 'getBasicModelConfigForDisableAction').and.returnValue({
        title: 'Confirm', message: 'Are you sure?', btnOkText: 'Yes', btnCancelText: 'No'
      });
      spyOn(mockConfirmDialogService, 'confirm').and.returnValue(Promise.resolve(false));

      await component.validateProductStatusForDisableAction();

      expect(deviceOperationServiceSpy.disableProductStatusForDevices).not.toHaveBeenCalled();
    });

    it('should handle dialog dismissal during disable confirmation', async () => {
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);
      const mockConfirmDialogService = TestBed.inject(ConfirmDialogService);
      spyOn(mockConfirmDialogService, 'getBasicModelConfigForDisableAction').and.returnValue({
        title: 'Confirm', message: 'Are you sure?', btnOkText: 'Yes', btnCancelText: 'No'
      });
      spyOn(mockConfirmDialogService, 'confirm').and.returnValue(Promise.reject('Dialog dismissed'));

      await component.validateProductStatusForDisableAction();

      expect(deviceOperationServiceSpy.disableProductStatusForDevices).not.toHaveBeenCalled();
      expect(component.loading).toBe(false);
    });
  });

  describe('validateProductStatusForRMAAction - comprehensive', () => {
    it('should handle user canceling the RMA confirmation', async () => {
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);
      const mockConfirmDialogService = TestBed.inject(ConfirmDialogService);
      spyOn(mockConfirmDialogService, 'getBasicModelConfigForRMAAction').and.returnValue({
        title: 'Confirm RMA', message: 'Are you sure?', btnOkText: 'Yes', btnCancelText: 'No'
      });
      spyOn(mockConfirmDialogService, 'confirm').and.returnValue(Promise.resolve(false));

      await component.validateProductStatusForRMAAction();

      expect(deviceOperationServiceSpy.rmaProductStatusForDevices).not.toHaveBeenCalled();
    });

    it('should handle dialog dismissal during RMA confirmation', async () => {
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);
      const mockConfirmDialogService = TestBed.inject(ConfirmDialogService);
      spyOn(mockConfirmDialogService, 'getBasicModelConfigForRMAAction').and.returnValue({
        title: 'Confirm RMA', message: 'Are you sure?', btnOkText: 'Yes', btnCancelText: 'No'
      });
      spyOn(mockConfirmDialogService, 'confirm').and.returnValue(Promise.reject('Dialog dismissed'));

      await component.validateProductStatusForRMAAction();

      expect(deviceOperationServiceSpy.rmaProductStatusForDevices).not.toHaveBeenCalled();
      expect(component.loading).toBe(false);
    });
  });

  // ==================== PRIVATE METHOD TESTS ====================

  describe('setLoadingStatus', () => {
    it('should set loading status to true', () => {
      component.loading = false;
      (component as any).setLoadingStatus(true);
      expect(component.loading).toBe(true);
    });

    it('should set loading status to false', () => {
      component.loading = true;
      (component as any).setLoadingStatus(false);
      expect(component.loading).toBe(false);
    });
  });

  describe('resetPage', () => {
    it('should reset page and previousPage values', () => {
      component.page = 5;
      component.previousPage = 3;
      (component as any).resetPage();
      expect(component.page).toBe(0);
      expect(component.previousPage).toBe(1);
    });
  });

  describe('subjectInit - comprehensive', () => {
    it('should handle device filter action with default page number', () => {
      const mockDeviceFilterAction: DeviceFilterAction = {
        listingPageReloadSubjectParameter: {
          isReloadData: true,
          isDefaultPageNumber: true,
          isClearFilter: false,
          isMovePrevPage: false,
          isOtherAction: false
        },
        deviceSearchRequest: new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null)
      };

      component.deviceIdList = [1, 2, 3];
      component.selectedDeviceList = deviceList;
      spyOn(component, 'loadAll');
      spyOn(component as any, 'resetPage');

      (component as any).subjectInit();
      const deviceFilterSubject = deviceOperationServiceSpy.getDeviceListFilterRequestParameterSubject();
      deviceFilterSubject.next(mockDeviceFilterAction);

      expect(component.deviceIdList).toEqual([]);
      expect(component.selectedDeviceList).toEqual([]);
      expect(component.loadAll).toHaveBeenCalledWith(mockDeviceFilterAction.deviceSearchRequest);
    });

    it('should handle device filter action without default page number', () => {
      const mockDeviceFilterAction: DeviceFilterAction = {
        listingPageReloadSubjectParameter: {
          isReloadData: true,
          isDefaultPageNumber: false,
          isClearFilter: false,
          isMovePrevPage: false,
          isOtherAction: false
        },
        deviceSearchRequest: new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null)
      };

      component.deviceIdList = [1, 2, 3];
      component.selectedDeviceList = deviceList;
      spyOn(component, 'loadAll');

      (component as any).subjectInit();
      const deviceFilterSubject = deviceOperationServiceSpy.getDeviceListFilterRequestParameterSubject();
      deviceFilterSubject.next(mockDeviceFilterAction);

      expect(component.deviceIdList).toEqual([1, 2, 3]); // Should not be cleared
      expect(component.selectedDeviceList).toEqual(deviceList); // Should not be cleared
      expect(component.loadAll).toHaveBeenCalledWith(mockDeviceFilterAction.deviceSearchRequest);
    });

    it('should not reload data when isReloadData is false', () => {
      const mockDeviceFilterAction: DeviceFilterAction = {
        listingPageReloadSubjectParameter: {
          isReloadData: false,
          isDefaultPageNumber: true,
          isClearFilter: false,
          isMovePrevPage: false,
          isOtherAction: false
        },
        deviceSearchRequest: new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null)
      };

      spyOn(component, 'loadAll');

      (component as any).subjectInit();
      const deviceFilterSubject = deviceOperationServiceSpy.getDeviceListFilterRequestParameterSubject();
      deviceFilterSubject.next(mockDeviceFilterAction);

      expect(component.loadAll).not.toHaveBeenCalled();
    });
  });

  // ==================== INITIALIZATION TESTS ====================

  describe('ngOnInit - comprehensive scenarios', () => {
    it('should initialize with all permissions false', () => {
      // Create a fresh component instance for this test
      const freshFixture = TestBed.createComponent(DeviceComponent);
      const freshComponent = freshFixture.componentInstance;

      authServiceSpy.isAuthenticate.and.returnValue(true);
      permissionServiceSpy.getDevicePermission.and.returnValues(false, false, false, false, false, false, false);
      spyOn(freshComponent, 'getDeviceData');

      freshComponent.ngOnInit();

      expect(freshComponent.checkboxDisplayPermission).toBe(false);
      expect(freshComponent.getDeviceData).not.toHaveBeenCalled();
    });

    it('should initialize with some permissions true', () => {
      // Create a fresh component instance for this test
      const freshFixture = TestBed.createComponent(DeviceComponent);
      const freshComponent = freshFixture.componentInstance;

      authServiceSpy.isAuthenticate.and.returnValue(true);
      permissionServiceSpy.getDevicePermission.and.returnValues(true, true, false, false, false, false, false);
      spyOn(freshComponent, 'getDeviceData');

      freshComponent.ngOnInit();

      expect(freshComponent.checkboxDisplayPermission).toBe(true);
      expect(freshComponent.getDeviceData).toHaveBeenCalled();
    });

    it('should initialize product status list', () => {
      // Create a fresh component instance for this test
      const freshFixture = TestBed.createComponent(DeviceComponent);
      const freshComponent = freshFixture.componentInstance;

      authServiceSpy.isAuthenticate.and.returnValue(true);
      permissionServiceSpy.getDevicePermission.and.returnValue(true);
      const mockProductStatusList = [{ key: 'ENABLED', value: 'Enabled' }];
      const keyValueMappingService = TestBed.inject(KeyValueMappingServiceService);
      spyOn(keyValueMappingService, 'enumOptionToList').and.returnValue(mockProductStatusList);
      spyOn(freshComponent, 'getDeviceData');

      freshComponent.ngOnInit();

      expect(freshComponent.productStatusList).toEqual(mockProductStatusList);
    });
  });

  // ==================== ERROR HANDLING TESTS ====================

  describe('error handling in async methods', () => {
    it('should handle errors in convertDataToClient', async () => {
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.convertDevicesToClient.and.returnValue(Promise.reject(new Error('Conversion Error')));
      spyOn(component, 'clearDeviceIdCheckBox');

      await component.convertDataToClient();

      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });

    it('should handle errors in convertDataToDemo', async () => {
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.convertDevicesToDemo.and.returnValue(Promise.reject(new Error('Conversion Error')));
      spyOn(component, 'clearDeviceIdCheckBox');

      await component.convertDataToDemo();

      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });

    it('should handle errors in convertDataToTest', async () => {
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.convertDevicesToTest.and.returnValue(Promise.reject(new Error('Conversion Error')));
      spyOn(component, 'clearDeviceIdCheckBox');

      await component.convertDataToTest();

      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });

    it('should handle errors in lockUnlock', async () => {
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.lockUnlockDevices.and.returnValue(Promise.reject(new Error('Lock Error')));
      spyOn(component, 'clearDeviceIdCheckBox');

      await component.lockUnlock(true);

      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });

    it('should handle errors in enableDisableDevice', async () => {
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.enableDisableDevices.and.returnValue(Promise.reject(new Error('Enable Error')));
      spyOn(component, 'clearDeviceIdCheckBox');

      await component.enableDisableDevice(true);

      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });

    it('should handle errors in exportCSV', async () => {
      component.deviceIdList = [1, 2];
      deviceOperationServiceSpy.exportDeviceCSV.and.returnValue(Promise.reject(new Error('Export Error')));
      spyOn(component, 'clearDeviceIdCheckBox');

      await component.exportCSV();

      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });

    it('should handle errors in associationDeviceWithSalesOrder', async () => {
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);
      deviceOperationServiceSpy.associateDevicesWithSalesOrder.and.returnValue(Promise.reject(new Error('Association Error')));
      spyOn(component, 'clearDeviceIdCheckBox');
      customerAssociationServiceSpy.openCustomerAssociationPopup.and.returnValue(Promise.resolve({
        button: true, basicSalesOrderDetailResponse: mockBasicSalesOrderDetailResponse, isSalesOrderNewAdd: false
      }));

      await component.associationDeviceWithSalesOrder();

      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });

    it('should handle errors in validateProductStatusForDisableAction', async () => {
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);
      deviceOperationServiceSpy.disableProductStatusForDevices.and.returnValue(Promise.reject(new Error('Disable Error')));
      spyOn(component, 'clearDeviceIdCheckBox');
      const mockConfirmDialogService = TestBed.inject(ConfirmDialogService);
      spyOn(mockConfirmDialogService, 'getBasicModelConfigForDisableAction').and.returnValue({
        title: 'Confirm', message: 'Are you sure?', btnOkText: 'Yes', btnCancelText: 'No'
      });
      spyOn(mockConfirmDialogService, 'confirm').and.returnValue(Promise.resolve(true));

      await component.validateProductStatusForDisableAction();

      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });

    it('should handle errors in validateProductStatusForRMAAction', async () => {
      component.deviceIdList = [1, 2];
      component.selectedDeviceList = deviceList.slice(0, 2);
      deviceOperationServiceSpy.validateDeviceSelection.and.returnValue(true);
      deviceOperationServiceSpy.rmaProductStatusForDevices.and.returnValue(Promise.reject(new Error('RMA Error')));
      spyOn(component, 'clearDeviceIdCheckBox');
      const mockConfirmDialogService = TestBed.inject(ConfirmDialogService);
      spyOn(mockConfirmDialogService, 'getBasicModelConfigForRMAAction').and.returnValue({
        title: 'Confirm RMA', message: 'Are you sure?', btnOkText: 'Yes', btnCancelText: 'No'
      });
      spyOn(mockConfirmDialogService, 'confirm').and.returnValue(Promise.resolve(true));

      await component.validateProductStatusForRMAAction();

      expect(component.loading).toBe(false);
      expect(component.clearDeviceIdCheckBox).toHaveBeenCalled();
    });
  });

  // ==================== CONSTRUCTOR TESTS ====================

  describe('constructor', () => {
    it('should initialize form and myForm', () => {
      expect(component.form).toBeDefined();
      expect(component.myForm).toBeDefined();
      expect(component.itemsPerPage).toBe(ITEMS_PER_PAGE);
    });
  });

  // ==================== ADDITIONAL EDGE CASES ====================

  describe('additional edge cases', () => {
    it('should handle getDeviceData with different initial values', () => {
      component.page = 10;
      component.drpselectsize = 50;
      component.itemsPerPage = 100;
      spyOn(component, 'clearDeviceIdCheckBox');
      spyOn(component, 'updateIsFilterHidden');
      spyOn(component, 'updateIsFilterComponentInitWithApicall');
      spyOn(component, 'updateListPageRefreshForbackToDetailPage');
      const commonsService = TestBed.inject(CommonsService);
      spyOn(commonsService, 'accessDataSizes').and.returnValue(['10', '25', '50', '100']);

      component.getDeviceData();

      expect(component.page).toBe(0);
      expect(component.drpselectsize).toBe(ITEMS_PER_PAGE);
      expect(component.itemsPerPage).toBe(ITEMS_PER_PAGE);
      expect(component.dataSizes).toEqual(['10', '25', '50', '100']);
    });

    it('should handle refreshFilter correctly', () => {
      component.page = 5;
      component.previousPage = 3;
      spyOn(component, 'filterPageSubjectCallForReloadPage');

      component.refreshFilter();

      expect(component.page).toBe(0);
      expect(component.previousPage).toBe(1);
      expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(true, true);
    });

    it('should handle filterPageSubjectCallForReloadPage with different parameters', () => {
      const mockSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);
      component.deviceSearchRequestBody = mockSearchRequest;
      component.isFilterHidden = true;

      component.filterPageSubjectCallForReloadPage(false, true);

      expect(deviceOperationServiceSpy.callRefreshPageSubject).toHaveBeenCalledWith(
        jasmine.objectContaining({
          isReloadData: true,
          isDefaultPageNumber: false,
          isClearFilter: true,
          isMovePrevPage: false
        }),
        component.deviceListResource,
        true,
        mockSearchRequest
      );
    });
  });
});